/* New Color Scheme for REIN Website */
:root {
    /* Main colors */
    --primary-color: #336021;
    --secondary-color: #E68C3A;
    --dark-color: #272525;
    --light-color: #F4F2EF;
    --white-color: #FFFFFF;
    --gray-color: #666666;

    /* Lighter and darker variations */
    --primary-light: #3d7228;
    --primary-dark: #294d1a;
    --secondary-light: #eb9d57;
    --secondary-dark: #d47a29;
    --dark-light: #3a3838;
    --dark-dark: #1a1919;

    /* Transparent variations for overlays and backgrounds */
    --primary-10: rgba(51, 96, 33, 0.1);
    --primary-20: rgba(51, 96, 33, 0.2);
    --primary-50: rgba(51, 96, 33, 0.5);
    --primary-80: rgba(51, 96, 33, 0.8);
    --dark-10: rgba(39, 37, 37, 0.1);
    --dark-20: rgba(39, 37, 37, 0.2);
    --dark-50: rgba(39, 37, 37, 0.5);
    --dark-80: rgba(39, 37, 37, 0.8);
    --secondary-10: rgba(230, 140, 58, 0.1);
    --secondary-20: rgba(230, 140, 58, 0.2);
    --secondary-50: rgba(230, 140, 58, 0.5);

    /* Text colors */
    --text-dark: var(--dark-color);
    --text-light: var(--white-color);
    --text-muted: #6c757d;

    /* UI Elements */
    --shadow-sm: 0 5px 15px rgba(0,0,0,0.08);
    --shadow-md: 0 10px 30px rgba(0,0,0,0.12);
    --shadow-lg: 0 15px 35px rgba(0,0,0,0.15);

    /* Transitions */
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;
}

/* Apply new color scheme to existing elements */
body {
    color: var(--text-dark);
    background-color: var(--light-color);
    font-family: 'Poppins', sans-serif;
}

/* Text colors */
.text-primary {
    color: var(--primary-color) !important;
}

.text-secondary {
    color: var(--secondary-color) !important;
}

/* Background colors */
.bg-primary {
    background-color: var(--primary-color) !important;
}

.bg-secondary {
    background-color: var(--secondary-color) !important;
}

.bg-dark {
    background-color: var(--dark-color) !important;
}

.bg-light {
    background-color: var(--light-color) !important;
}

/* Buttons */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover, .btn-primary:focus, .btn-primary:active {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-secondary:hover, .btn-secondary:focus, .btn-secondary:active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Header styling */
.header-four {
    background-color: var(--dark-80);
}

.header-four .nav-link {
    color: var(--text-light) !important;
    transition: var(--transition-normal);
}

.header-four .nav-link.active,
.header-four .nav-link:hover {
    color: var(--secondary-color) !important;
}

.header-four .contact {
    background-color: var(--primary-color);
    color: var(--text-light);
    transition: var(--transition-normal);
    padding: 10px 25px;
    border-radius: 50px;
    font-weight: 600;
}

.header-four .contact:hover {
    background-color: var(--secondary-color);
    transform: translateY(-5px);
    box-shadow: 0 10px 25px var(--secondary-20);
}

/* Banner area styling */
.banner-overlay {
    background: transparent; /* Completely removed overlay to make it transparent */
}

.banner-tag {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text-light);
    padding: 10px 25px;
    border-radius: 50px;
    font-weight: 600;
    margin-bottom: 20px;
}

.banner-description {
    color: var(--light-color);
    font-size: 1.1rem;
    margin-bottom: 30px;
    max-width: 600px;
}

.banner-buttons {
    display: flex;
    align-items: center;
}

.under-line-button {
    color: var(--text-light);
    font-weight: 600;
    position: relative;
    padding-bottom: 5px;
    margin-right: 30px;
    transition: var(--transition-normal);
}

.under-line-button::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--secondary-color);
    transition: var(--transition-normal);
}

.under-line-button:hover {
    color: var(--secondary-color);
}

.under-line-button:hover::after {
    width: 50%;
}

.ghost-button {
    border: 2px solid var(--text-light);
    color: var(--text-light);
    background-color: transparent;
    padding: 12px 30px;
    border-radius: 50px;
    font-weight: 600;
    transition: var(--transition-normal);
    display: inline-block;
}

.ghost-button:hover {
    background-color: var(--text-light);
    color: var(--primary-color);
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(255, 255, 255, 0.2);
}

.social-wrapper-one-horizental a {
    background-color: var(--dark-20);
    transition: var(--transition-normal);
}

.social-wrapper-one-horizental a:hover {
    background-color: var(--secondary-color);
    transform: translateY(-5px);
}

/* About section styling */
.section-tag {
    display: inline-block;
    background-color: var(--primary-10);
    color: var(--primary-color);
    padding: 8px 20px;
    border-radius: 50px;
    font-weight: 600;
    margin-bottom: 15px;
}

.title.skew-up {
    position: relative;
    margin-bottom: 30px;
    font-weight: 700;
}

.title.skew-up::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 0;
    width: 80px;
    height: 4px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
    transform: skewX(-30deg);
}

.engineer-experience-area {
    background-color: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: var(--shadow-sm);
    margin: 30px 0;
    transition: var(--transition-normal);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.engineer-experience-area:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-md);
}

.authora-area {
    display: flex;
    align-items: center;
}

.authora-area img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 3px solid var(--primary-color);
    margin-right: 15px;
}

.key-points {
    margin-bottom: 30px;
}

.key-point {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.key-point .icon {
    min-width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    background-color: var(--primary-10);
    color: var(--primary-color);
}

.button-circle-text {
    display: inline-flex;
    align-items: center;
    color: var(--primary-color);
    font-weight: 600;
    transition: var(--transition-normal);
}

.button-circle-text i {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: var(--text-light);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    transition: var(--transition-normal);
}

.button-circle-text:hover {
    color: var(--secondary-color);
}

.button-circle-text:hover i {
    background-color: var(--secondary-color);
    transform: rotate(45deg);
}

/* Service cards styling */
.service-card {
    background-color: var(--text-light);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    height: 100%;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-md);
}

.service-image img {
    width: 100%;
    height: 220px;
    object-fit: cover;
}

.service-content {
    padding: 25px;
    position: relative;
}

.service-content .icon {
    position: absolute;
    top: -30px;
    left: 25px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: var(--text-light);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-sm);
}

.service-content .icon img {
    width: 30px;
    height: 30px;
}

.service-category {
    display: inline-block;
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 10px;
}

.service-title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 15px;
}

.read-more {
    color: var(--secondary-color);
    font-weight: 600;
    display: inline-flex;
    align-items: center;
}

.read-more i {
    margin-left: 5px;
    transition: var(--transition-normal);
}

.read-more:hover i {
    transform: translateX(5px);
}

/* Why Choose Us section styling */
.feature-card {
    background-color: var(--text-light);
    border-radius: 15px;
    padding: 30px;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    height: 100%;
    text-align: center;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-md);
}

.feature-card .icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 28px;
}

.feature-card .icon.green {
    background-color: var(--primary-10);
    color: var(--primary-color);
}

.feature-card .icon.orange {
    background-color: var(--secondary-10);
    color: var(--secondary-color);
}

.feature-card h4 {
    font-size: 20px;
    margin-bottom: 15px;
}

/* Stats section styling */
.stats-section {
    background: linear-gradient(135deg, var(--dark-color), var(--primary-color));
    color: var(--text-light);
    padding: 80px 0;
}

.stat-item h2 {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 10px;
}

/* Testimonials section styling */
.testimonial-card {
    background-color: var(--light-color);
    border-radius: 15px;
    padding: 30px;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    height: 100%;
    margin-bottom: 30px;
}

.testimonial-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-md);
}

.quote-icon {
    font-size: 30px;
    margin-bottom: 20px;
    color: var(--secondary-color);
}

/* Footer styling */
.footer {
    background-color: var(--dark-color);
    color: var(--text-light);
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 10px;
    background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
}

.footer-title {
    color: var(--text-light);
    font-size: 20px;
    margin-bottom: 25px;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    transition: var(--transition-normal);
}

.footer-links a:hover {
    color: var(--secondary-color);
    padding-left: 5px;
}

/* Animations */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0) translateX(-50%);
    }
    40% {
        transform: translateY(-20px) translateX(-50%);
    }
    60% {
        transform: translateY(-10px) translateX(-50%);
    }
}

@keyframes scrollDown {
    0% {
        opacity: 1;
        transform: translateY(0) translateX(-50%);
    }
    100% {
        opacity: 0;
        transform: translateY(20px) translateX(-50%);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: var(--shadow-sm);
    }
    50% {
        transform: scale(1.05);
        box-shadow: var(--shadow-md);
    }
    100% {
        transform: scale(1);
        box-shadow: var(--shadow-sm);
    }
}
