/* Improved Layout CSS for REIN Website */

/* Additional styles for the new layout */
.highlight-green {
    color: #336021;
}

.highlight-orange {
    color: #E68C3A;
}

.highlight-light {
    color: #F4F2EF;
}

/* Banner section */
.banner-section {
    position: relative;
    height: 100vh;
}

.banner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent; /* Completely removed overlay to make it transparent */
    z-index: 1;
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3;
    text-align: center;
    animation: bounce 2s infinite;
}

.scroll-indicator p {
    color: #fff;
    margin-bottom: 10px;
    font-size: 0.9rem;
    letter-spacing: 1px;
}

.scroll-mouse {
    width: 30px;
    height: 50px;
    border: 2px solid #fff;
    border-radius: 15px;
    margin: 0 auto;
    position: relative;
}

.scroll-dot {
    width: 6px;
    height: 10px;
    background: #fff;
    border-radius: 3px;
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    animation: scrollDown 2s infinite;
}

/* About section */
.about-section {
    padding: 100px 0;
    background-color: #F4F2EF;
}

.about-content {
    padding-right: 30px;
}

.about-image {
    position: relative;
}

.main-image {
    width: 100%;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.12);
}

.floating-image {
    position: absolute;
    bottom: -30px;
    left: -30px;
    width: 50%;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.12);
    border: 5px solid white;
}

.experience-badge {
    position: absolute;
    top: 30px;
    right: -20px;
    background: linear-gradient(135deg, #336021, #272525);
    color: white;
    padding: 15px;
    border-radius: 50%;
    width: 100px;
    height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    animation: pulse 2s infinite;
}

.experience-badge .years {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
}

.experience-badge .text {
    font-size: 0.9rem;
}

.engineer-experience-area {
    background-color: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    margin: 30px 0;
    transition: all 0.4s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.engineer-experience-area:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.12);
}

.authora-area {
    display: flex;
    align-items: center;
}

.authora-area img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 3px solid #336021;
    margin-right: 15px;
}

.key-points {
    margin-bottom: 30px;
}

.key-point {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.key-point .icon {
    min-width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    background-color: rgba(51, 96, 33, 0.1);
    color: #336021;
}

/* Services section */
.services-section {
    padding: 100px 0;
    background-color: #fff;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 50px;
}

.service-card {
    background-color: #fff;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    transition: all 0.4s ease;
    height: 100%;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.12);
}

.service-image img {
    width: 100%;
    height: 220px;
    object-fit: cover;
}

.service-content {
    padding: 25px;
    position: relative;
}

.service-content .icon {
    position: absolute;
    top: -30px;
    left: 25px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.service-content .icon img {
    width: 30px;
    height: 30px;
}

.service-category {
    display: inline-block;
    color: #336021;
    font-weight: 500;
    margin-bottom: 10px;
}

.service-title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 15px;
}

.read-more {
    color: #E68C3A;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
}

.read-more i {
    margin-left: 5px;
    transition: all 0.3s ease;
}

.read-more:hover i {
    transform: translateX(5px);
}

/* Why Choose Us section */
.why-choose-section {
    padding: 100px 0;
    background-color: #F4F2EF;
}

.section-description {
    max-width: 700px;
    margin: 20px auto 0;
}

.feature-card {
    background-color: #fff;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    transition: all 0.4s ease;
    height: 100%;
    text-align: center;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.12);
}

.feature-card .icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 28px;
}

.feature-card .icon.green {
    background-color: rgba(51, 96, 33, 0.1);
    color: #336021;
}

.feature-card .icon.orange {
    background-color: rgba(230, 140, 58, 0.1);
    color: #E68C3A;
}

.feature-card h4 {
    font-size: 20px;
    margin-bottom: 15px;
}

/* Stats section */
.stats-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #272525, #336021);
    color: #fff;
}

.stat-item {
    padding: 20px;
}

.stat-item h2 {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 10px;
}

.stat-item p {
    font-size: 18px;
    margin-bottom: 0;
}

/* Testimonials section */
.testimonials-section {
    padding: 100px 0;
    background-color: #fff;
}

.testimonial-card {
    background-color: #F4F2EF;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    transition: all 0.4s ease;
    height: 100%;
    margin-bottom: 30px;
}

.testimonial-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.12);
}

.quote-icon {
    font-size: 30px;
    margin-bottom: 20px;
    color: #E68C3A;
}

.testimonial-text {
    font-style: italic;
    margin-bottom: 20px;
}

.client-info {
    display: flex;
    align-items: center;
}

.client-info img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-right: 15px;
}

.client-info h5 {
    font-size: 18px;
    margin-bottom: 5px;
}

.client-info span {
    color: #666;
}

/* Contact section */
.contact-section {
    padding: 100px 0;
    background-color: #F4F2EF;
}

.contact-form-wrapper {
    background-color: #fff;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    height: 100%;
}

.form-group {
    margin-bottom: 20px;
}

.form-control {
    height: 50px;
    border-radius: 10px;
    border: 1px solid #e0e0e0;
    padding: 10px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #336021;
    box-shadow: 0 0 0 3px rgba(51, 96, 33, 0.1);
}

textarea.form-control {
    height: auto;
}

.btn-primary {
    background-color: #336021;
    border: none;
    padding: 12px 30px;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #E68C3A;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(230, 140, 58, 0.4);
}

.contact-info-wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.contact-info {
    display: flex;
    align-items: flex-start;
    margin-bottom: 30px;
    background-color: #fff;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
}

.contact-info .icon {
    min-width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: rgba(51, 96, 33, 0.1);
    color: #336021;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin-right: 15px;
}

.contact-info h5 {
    font-size: 18px;
    margin-bottom: 5px;
}

.contact-info p {
    margin-bottom: 5px;
    color: #666;
}

.contact-map {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
}

/* Footer */
.footer {
    background-color: #272525;
    color: #fff;
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 10px;
    background: linear-gradient(to right, #336021, #E68C3A);
}

.footer-top {
    padding: 80px 0 50px;
}

.footer-logo {
    max-width: 150px;
    margin-bottom: 20px;
}

.social-links {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.social-links a {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background-color: #E68C3A;
    transform: translateY(-5px);
}

.footer-title {
    font-size: 20px;
    margin-bottom: 25px;
    color: #fff;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s ease;
}

.footer-links a:hover {
    color: #E68C3A;
    padding-left: 5px;
}

.newsletter-form {
    position: relative;
    margin-top: 20px;
}

.newsletter-form input {
    width: 100%;
    height: 50px;
    border-radius: 50px;
    border: none;
    padding: 0 60px 0 20px;
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
}

.newsletter-form button {
    position: absolute;
    top: 0;
    right: 0;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    background-color: #336021;
    color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
}

.newsletter-form button:hover {
    background-color: #E68C3A;
}

.footer-bottom {
    padding: 20px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
    margin: 0;
    color: rgba(255, 255, 255, 0.7);
}

/* Responsive styles */
@media (max-width: 991px) {
    .section-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .button-area {
        margin-top: 20px;
    }

    .about-image {
        margin-top: 50px;
    }

    .floating-image {
        width: 40%;
    }

    .contact-info-wrapper {
        margin-top: 50px;
    }
}

@media (max-width: 767px) {
    .banner-section h1.title {
        font-size: 2.5rem;
    }

    .banner-buttons {
        flex-direction: column;
    }

    .ghost-button {
        margin-top: 15px;
    }

    .section-header {
        text-align: center;
        align-items: center;
    }

    .title.skew-up::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .engineer-experience-area {
        flex-direction: column;
    }

    .experience-area {
        margin-top: 20px;
    }

    .floating-image {
        display: none;
    }

    .experience-badge {
        width: 80px;
        height: 80px;
    }

    .experience-badge .years {
        font-size: 1.5rem;
    }

    .contact-form-wrapper {
        margin-bottom: 30px;
    }
}

/* Base styles and resets */
body {
    font-family: 'Poppins', sans-serif;
    background-color: #F4F2EF;
    color: #272525;
    line-height: 1.6;
    overflow-x: hidden;
}

section {
    padding: 80px 0;
    position: relative;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 20px;
}

p {
    margin-bottom: 20px;
    font-size: 16px;
}

a {
    text-decoration: none;
    transition: all 0.3s ease;
}

.container {
    max-width: 1200px;
    padding: 0 15px;
    margin: 0 auto;
}

/* Header improvements */
.header-four {
    background-color: rgba(39, 37, 37, 0.9) !important;
    padding: 15px 0;
    position: fixed;
    width: 100%;
    z-index: 1000;
    transition: all 0.4s ease;
}

.header-four .logo-area img {
    max-height: 50px;
}

.header-four .nav-link {
    color: #fff !important;
    font-weight: 500;
    padding: 10px 15px;
    position: relative;
}

.header-four .contact {
    background-color: #E68C3A !important;
    color: #fff;
    padding: 10px 25px;
    border-radius: 50px;
    font-weight: 600;
    box-shadow: 0 5px 15px rgba(230, 140, 58, 0.3);
}

.header-four .contact:hover {
    background-color: #336021 !important;
    transform: translateY(-3px);
}

/* Banner section improvements */
.rts-banner-six-area {
    height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.rts-banner-six-area video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.banner-overlay {
    background: transparent !important;
}

.banner-six-inner-content-wrapper {
    padding: 100px 0;
}

.banner-tag {
    background-color: rgba(230, 140, 58, 0.3) !important;
    backdrop-filter: blur(5px);
    padding: 8px 20px;
    border-radius: 50px;
    color: #fff;
    font-weight: 500;
    display: inline-block;
    margin-bottom: 20px;
}

.inner-content h1.title {
    font-size: 3.5rem;
    font-weight: 800;
    color: #fff;
    margin-bottom: 25px;
    line-height: 1.2;
    text-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

.banner-description {
    font-size: 2.2rem;
    color: rgba(255, 255, 255, 0.95);
    max-width: 800px;
    margin-bottom: 30px;
    font-weight: 600;
    line-height: 1.4;
    text-shadow: 0 3px 12px rgba(0,0,0,0.5);
    letter-spacing: 0.5px;
}

.banner-buttons {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.under-line-button {
    background-color: #E68C3A !important;
    color: #fff;
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: 600;
    display: inline-block;
    box-shadow: 0 5px 15px rgba(230, 140, 58, 0.3);
    transition: all 0.3s ease;
}

.under-line-button:hover {
    background-color: #336021 !important;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(51, 96, 33, 0.4);
}

.ghost-button {
    border: 2px solid #fff;
    color: #fff;
    padding: 15px 30px;
    border-radius: 50px;
    font-weight: 600;
    display: inline-block;
    transition: all 0.3s ease;
}

.ghost-button:hover {
    background-color: #E68C3A;
    border-color: #E68C3A;
    color: #fff;
    transform: translateY(-5px);
}

/* Social media icons */
.banner-social-rotate {
    position: absolute;
    right: 30px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
}

.social-wrapper-one-horizental {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.social-wrapper-one-horizental a {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(51, 96, 33, 0.3);
    color: #fff;
    transition: all 0.3s ease;
}

.social-wrapper-one-horizental a:hover {
    background-color: #E68C3A !important;
    transform: translateY(-3px);
}

/* Section styling */
.section-title {
    text-align: center;
    margin-bottom: 60px;
}

.section-tag {
    display: inline-block;
    background-color: rgba(51, 96, 33, 0.1);
    color: #336021;
    padding: 8px 20px;
    border-radius: 50px;
    font-weight: 500;
    margin-bottom: 15px;
}

.title.skew-up {
    position: relative;
    display: inline-block;
    margin-bottom: 30px;
}

.title.skew-up::after {
    content: '';
    position: absolute;
    width: 80px;
    height: 4px;
    background: linear-gradient(to right, #336021, #E68C3A);
    bottom: -15px;
    left: 0;
    border-radius: 2px;
}

/* Card styling */
.card {
    background-color: #fff;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    transition: all 0.4s ease;
    height: 100%;
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.12);
}

.card-img {
    width: 100%;
    height: 220px;
    object-fit: cover;
}

.card-body {
    padding: 25px;
}

.card-title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 15px;
}

.card-text {
    color: #666;
    margin-bottom: 20px;
}

/* Button styling */
.btn {
    display: inline-block;
    padding: 12px 30px;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #336021;
    color: #fff;
    box-shadow: 0 5px 15px rgba(51, 96, 33, 0.3);
}

.btn-primary:hover {
    background-color: #E68C3A;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(230, 140, 58, 0.4);
}

.btn-outline {
    border: 2px solid #336021;
    color: #336021;
}

.btn-outline:hover {
    background-color: #336021;
    color: #fff;
    transform: translateY(-5px);
}

/* Footer styling */
.footer {
    background-color: #272525;
    color: #fff;
    padding: 80px 0 30px;
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 10px;
    background: linear-gradient(to right, #336021, #E68C3A);
}

.footer-title {
    font-size: 20px;
    margin-bottom: 25px;
    color: #fff;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
}

.footer-links a:hover {
    color: #E68C3A;
    padding-left: 5px;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
    margin-top: 50px;
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
}
