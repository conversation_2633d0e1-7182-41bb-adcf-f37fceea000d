<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- favicon -->
    <link rel="shortcut icon" type="image/x-icon" href="images/fav.png">
    <title>REIN - Revolution Energy India</title>
    <meta name="description" content="REIN - Sustainable energy solutions for a greener future. Solar, wind, and renewable energy services.">

    <!-- fontawesome css -->
    <link rel="stylesheet" href="css/fontawesome-6.css">
    <!-- fontawesome css -->
    <link rel="stylesheet" href="css/swiper.css">
    <link rel="stylesheet" href="css/unicons.css">
    <link rel="stylesheet" href="css/metimenu.css">
    <link rel="stylesheet" href="css/animate.css">
    <!-- bootstrap css -->
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <!-- Custom css -->
    <link rel="stylesheet" href="css/style.css">
    <!-- Custom color palette -->
    <link rel="stylesheet" href="css/custom-palette.css">
    <!-- New color scheme -->
    <link rel="stylesheet" href="css/new-color-scheme.css">
    <!-- Improved layout -->
    <link rel="stylesheet" href="css/improved-layout.css">
    <!-- Mobile responsive styles -->
    <link rel="stylesheet" href="css/mobile-responsive.css">

    <!-- Custom styles for about section, banner buttons, and contact info -->
    <style>
        /* Mobile navbar styles - simplified */
        /* Bootstrap Dropdown Menu Styling */
        .dropdown-menu {
            border-radius: 10px;
            border: none;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 15px 0;
            margin-top: 10px;
            min-width: 250px;
            animation: fadeIn 0.3s ease;
        }

        .dropdown-item {
            padding: 10px 20px;
            color: var(--text-dark);
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .dropdown-item:hover, .dropdown-item:focus {
            background-color: var(--primary-10);
            color: var(--primary-color);
            padding-left: 25px;
        }

        .dropdown-divider {
            margin: 8px 20px;
            border-color: var(--primary-10);
        }

        .dropdown-toggle::after {
            display: none;
        }

        .dropdown-toggle .fa-chevron-down {
            font-size: 12px;
            margin-left: 5px;
            transition: transform 0.3s ease;
        }

        .dropdown.show .dropdown-toggle .fa-chevron-down {
            transform: rotate(180deg);
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Enhanced Let's Talk button styling */
        .contact-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            box-shadow: 0 4px 15px rgba(51, 96, 33, 0.2);
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .contact-btn:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
            z-index: -1;
            transition: opacity 0.3s ease;
            opacity: 0;
        }

        .contact-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(51, 96, 33, 0.3);
            color: white;
        }

        .contact-btn:hover:before {
            opacity: 1;
        }

        .contact-btn .btn-text {
            margin-right: 10px;
        }

        .contact-btn .btn-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.3s ease;
        }

        .contact-btn:hover .btn-icon {
            transform: translateX(4px);
        }

        /* Contact info responsive styling */
        .contact-info-wrapper {
            background-color: var(--white-color);
            border-radius: 15px;
            box-shadow: var(--shadow-sm);
            padding: 30px;
            margin-top: 30px;
        }

        .contact-info {
            display: flex;
            align-items: flex-start;
            margin-bottom: 25px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .contact-info:hover {
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            transform: translateY(-5px);
        }

        .contact-info .icon {
            width: 50px;
            height: 50px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            flex-shrink: 0;
            margin-right: 15px;
        }

        .contact-info .content {
            flex-grow: 1;
            overflow: hidden;
        }

        .contact-info .content h5 {
            font-size: 18px;
            margin-bottom: 8px;
            color: var(--text-dark);
        }

        .contact-info .content p {
            margin-bottom: 5px;
            font-size: 16px;
            line-height: 1.4;
            color: var(--text-dark);
        }

        .contact-info .content a {
            color: var(--primary-color);
            text-decoration: none;
            word-break: break-word;
            display: inline-block;
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            transition: color 0.3s ease;
        }

        .email-container {
            max-width: 100%;
            overflow: hidden;
        }

        .email-container a {
            font-size: 15px;
            white-space: normal;
            overflow-wrap: break-word;
            word-wrap: break-word;
        }

        .address-line {
            margin-bottom: 3px;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .contact-info .content a:hover {
            color: var(--secondary-color);
        }

        .contact-map {
            margin-top: 20px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        @media (max-width: 767px) {
            .contact-info-wrapper {
                padding: 20px;
            }

            .contact-info {
                padding: 12px;
                margin-bottom: 15px;
            }

            .contact-info .icon {
                width: 40px;
                height: 40px;
                font-size: 16px;
                margin-right: 12px;
            }

            .contact-info .content h5 {
                font-size: 16px;
                margin-bottom: 5px;
            }

            .contact-info .content p,
            .contact-info .content a {
                font-size: 14px;
                line-height: 1.3;
                word-break: break-word;
            }

            .email-container {
                width: 100%;
            }

            .email-container a {
                font-size: 13px;
                display: inline-block;
                width: 100%;
            }

            .address-line {
                font-size: 14px;
                line-height: 1.3;
            }
        }

        @media (max-width: 575px) {
            .contact-info {
                flex-direction: column;
                align-items: center;
                text-align: center;
            }

            .contact-info .icon {
                margin-right: 0;
                margin-bottom: 10px;
            }

            .contact-info .content {
                width: 100%;
            }

            .contact-info .content a {
                display: block;
                width: 100%;
                overflow-wrap: break-word;
                word-wrap: break-word;
                -ms-word-break: break-all;
                word-break: break-all;
                word-break: break-word;
                -ms-hyphens: auto;
                -moz-hyphens: auto;
                -webkit-hyphens: auto;
                hyphens: auto;
            }

            .email-container {
                width: 100%;
            }

            .email-container a {
                font-size: 12px;
                padding: 0 5px;
                line-height: 1.4;
            }

            .address-line {
                font-size: 12px;
                line-height: 1.3;
                padding: 0 5px;
                margin-bottom: 2px;
            }
        }

        /* Banner buttons styling */
        .banner-buttons {
            display: flex;
            gap: 20px;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .banner-buttons a {
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            text-align: center;
            min-width: 220px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .banner-buttons a.under-line-button {
            background-color: var(--primary-color);
            color: white;
            border: 2px solid var(--primary-color);
            position: relative;
            overflow: hidden;
        }

        .banner-buttons a.under-line-button:hover {
            background-color: #3d9432;
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
        }

        .banner-buttons a.under-line-button::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 0;
            background-color: rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .banner-buttons a.under-line-button:hover::before {
            height: 100%;
        }

        .banner-buttons a.ghost-button {
            background-color: transparent;
            color: white;
            border: 2px solid white;
        }

        .banner-buttons a.ghost-button:hover {
            background-color: white;
            color: var(--secondary-color);
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
        }

        /* Scroll indicator styling */
        .scroll-indicator {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            z-index: 10;
            animation: fadeInUp 1s ease-in-out;
        }

        .scroll-indicator p {
            color: white;
            font-size: 12px;
            letter-spacing: 2px;
            margin-bottom: 10px;
            font-weight: 500;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .scroll-mouse {
            width: 30px;
            height: 50px;
            border: 2px solid white;
            border-radius: 20px;
            position: relative;
            display: flex;
            justify-content: center;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .scroll-dot {
            width: 6px;
            height: 6px;
            background-color: white;
            border-radius: 50%;
            position: absolute;
            top: 10px;
            animation: scrollAnimation 2s infinite;
        }

        @keyframes scrollAnimation {
            0% {
                opacity: 1;
                transform: translateY(0);
            }
            100% {
                opacity: 0;
                transform: translateY(20px);
            }
        }

        @media (max-width: 767px) {
            .banner-buttons {
                flex-direction: column;
                gap: 15px;
                align-items: center;
            }

            .banner-buttons a {
                width: 100%;
                max-width: 280px;
            }

            .scroll-indicator {
                bottom: 20px;
            }

            /* Mobile menu button removed */
        }

        /* About section styling */
        .about-section {
            padding: 100px 0;
            background-color: var(--light-color);
            position: relative;
            overflow: hidden;
        }

        .about-section .lead {
            font-size: 18px;
            line-height: 1.8;
            color: var(--text-color);
            margin-bottom: 30px;
        }

        .mission-vision-box {
            display: flex;
            gap: 20px;
            margin-top: 30px;
            margin-bottom: 30px;
        }

        .mission-box, .vision-box {
            flex: 1;
            background-color: var(--white-color);
            border-radius: 15px;
            padding: 25px;
            box-shadow: var(--shadow-sm);
            transition: var(--transition-normal);
            min-height: 220px;
            display: flex;
            flex-direction: column;
        }

        .mission-box:hover, .vision-box:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-md);
        }

        .mission-box .icon, .vision-box .icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            font-size: 24px;
        }

        .mission-box .icon.green, .vision-box .icon.green {
            background-color: rgba(74, 171, 61, 0.1);
            color: var(--primary-color);
        }

        .mission-box .icon.orange, .vision-box .icon.orange {
            background-color: rgba(253, 143, 20, 0.1);
            color: var(--secondary-color);
        }

        .mission-box h4, .vision-box h4 {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 10px;
            color: var(--text-dark);
        }

        .mission-box p, .vision-box p {
            font-size: 16px;
            line-height: 1.6;
            color: var(--text-dark);
            flex-grow: 1;
        }

        .team-highlight {
            background-color: var(--white-color);
            border-radius: 15px;
            padding: 25px;
            box-shadow: var(--shadow-sm);
            margin-top: 30px;
        }

        .team-member {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .team-photo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid var(--primary-color);
        }

        .team-info h5 {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 5px;
            color: var(--text-dark);
        }

        .team-info span {
            font-size: 14px;
            color: var(--primary-color);
            font-weight: 500;
        }

        .team-info p {
            font-size: 15px;
            font-style: italic;
            color: var(--text-light);
            margin-top: 10px;
        }

        .about-image-container {
            position: relative;
            height: 100%;
        }

        .main-image-wrapper {
            position: relative;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: var(--shadow-md);
        }

        .main-image {
            width: 100%;
            height: auto;
            border-radius: 20px;
            transition: transform 0.5s ease;
        }

        .main-image-wrapper:hover .main-image {
            transform: scale(1.05);
        }

        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.3));
            border-radius: 20px;
        }

        .floating-image {
            position: absolute;
            bottom: 30px;
            right: -30px;
            width: 200px;
            height: 150px;
            border-radius: 15px;
            box-shadow: var(--shadow-lg);
            z-index: 2;
            border: 5px solid var(--white-color);
        }

        .stats-cards {
            position: absolute;
            left: -20px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 15px;
            z-index: 2;
        }

        .stat-card {
            background-color: var(--white-color);
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            gap: 15px;
            width: 180px;
            transition: all 0.3s ease;
            border-left: 4px solid var(--primary-color);
        }

        .stat-card:hover {
            transform: translateX(10px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .stat-card .icon {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            flex-shrink: 0;
        }

        .stat-card.projects .icon {
            background-color: rgba(74, 171, 61, 0.15);
            color: var(--primary-color);
        }

        .stat-card.experience .icon {
            background-color: rgba(253, 143, 20, 0.15);
            color: var(--secondary-color);
            border-left: 4px solid var(--secondary-color);
        }

        .stat-card.satisfaction .icon {
            background-color: rgba(74, 171, 61, 0.15);
            color: var(--primary-color);
        }

        .stat-content {
            flex-grow: 1;
        }

        .stat-content h3 {
            font-size: 22px;
            font-weight: 700;
            margin-bottom: 2px;
            color: var(--text-dark);
        }

        .stat-content p {
            font-size: 13px;
            color: var(--text-dark);
            margin: 0;
            line-height: 1.3;
        }

        @media (max-width: 1199px) {
            .stats-cards {
                left: -10px;
                gap: 10px;
            }

            .stat-card {
                width: 160px;
                padding: 12px;
            }

            .stat-card .icon {
                width: 40px;
                height: 40px;
            }

            .floating-image {
                width: 180px;
                height: 130px;
                right: -20px;
            }
        }

        @media (max-width: 991px) {
            .mission-vision-box {
                flex-direction: column;
                gap: 15px;
            }

            .mission-box, .vision-box {
                min-height: auto;
                padding: 20px;
            }

            .stats-cards {
                position: static;
                flex-direction: row;
                transform: none;
                margin: 20px 0;
                justify-content: center;
                flex-wrap: wrap;
                gap: 15px;
            }

            .stat-card {
                width: 160px;
                margin-bottom: 0;
            }

            .stat-card:hover {
                transform: translateY(-5px);
            }

            .floating-image {
                position: static;
                margin: 20px auto 0;
                width: 100%;
                max-width: 300px;
                height: auto;
                display: block;
            }

            .about-image-container {
                margin-top: 30px;
            }

            .team-highlight {
                margin-bottom: 30px;
            }
        }

        /* Team Profile Card Styling */
        .team-profile-card {
            background-color: var(--white-color);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
        }

        .team-profile-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .profile-header {
            display: flex;
            align-items: center;
            padding: 25px;
            background: linear-gradient(135deg, var(--primary-color) 0%, #3d9432 100%);
            color: white;
            position: relative;
        }

        .profile-image-container {
            width: 90px;
            height: 90px;
            border-radius: 50%;
            overflow: hidden;
            border: 4px solid white;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            margin-right: 20px;
            flex-shrink: 0;
            position: relative;
            z-index: 1;
        }

        .profile-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }

        .profile-image-container:hover .profile-image {
            transform: scale(1.1);
        }

        .profile-title {
            flex-grow: 1;
        }

        .profile-title h3 {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
            color: white;
        }

        .profile-position {
            font-size: 16px;
            font-weight: 500;
            opacity: 0.9;
            display: inline-block;
            padding: 5px 12px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
        }

        .profile-content {
            padding: 25px;
        }

        .profile-quote {
            font-size: 16px;
            font-style: italic;
            color: var(--text-dark);
            margin-bottom: 20px;
            position: relative;
            padding-left: 20px;
            border-left: 3px solid var(--primary-color);
        }

        .profile-details {
            margin-bottom: 20px;
        }

        .profile-detail-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .profile-detail-item i {
            width: 30px;
            height: 30px;
            background-color: rgba(74, 171, 61, 0.1);
            color: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 14px;
        }

        .profile-detail-item span {
            font-size: 15px;
            color: var(--text-dark);
        }

        .profile-social {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .social-link {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .social-link.linkedin {
            background-color: #0077B5;
        }

        .social-link.email {
            background-color: var(--secondary-color);
        }

        .social-link:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
        }

        @media (max-width: 767px) {
            .about-section {
                padding: 60px 0;
            }

            .about-section .title {
                font-size: 28px;
                line-height: 1.4;
            }

            .about-section .lead {
                font-size: 16px;
            }

            .stats-cards {
                gap: 10px;
            }

            .stat-card {
                width: 140px;
                padding: 10px;
            }

            .stat-content h3 {
                font-size: 18px;
            }

            .stat-content p {
                font-size: 12px;
            }

            .team-member {
                flex-direction: column;
                text-align: center;
            }

            .team-photo {
                margin: 0 auto 15px;
            }

            /* Mobile styles for profile card */
            .profile-header {
                flex-direction: column;
                text-align: center;
                padding: 20px;
            }

            .profile-image-container {
                margin-right: 0;
                margin-bottom: 15px;
                width: 100px;
                height: 100px;
            }

            .profile-title h3 {
                font-size: 22px;
            }

            .profile-content {
                padding: 20px;
            }

            /* Mobile menu button removed */
        }
    </style>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>

<body>
    <!-- Header area start -->
    <header class="header-four header--sticky">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-2 col-md-3 col-6">
                    <div class="header-left">
                        <a href="index.html" class="logo-area">
                            <img src="images/logo-02.png" alt="logo">
                        </a>
                    </div>
                </div>
                <div class="col-lg-7 col-md-6 d-none d-md-block">
                    <div class="nav-area">
                        <!-- navigation area start -->
                        <div class="header-nav main-nav-one">
                            <nav>
                                <ul>
                                    <li>
                                        <a class="nav-link active" href="index.html">Home</a>
                                    </li>
                                    <li>
                                        <a class="nav-link" href="aboutus.html">About</a>
                                    </li>
                                    <li>
                                        <a class="nav-link" href="project.html">Project</a>
                                    </li>
                                    <li class="nav-item dropdown">
                                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            Services <i class="fa-solid fa-chevron-down"></i>
                                        </a>
                                        <ul class="dropdown-menu" aria-labelledby="navbarDropdown">
                                            <li><a class="dropdown-item" href="service.html">All Services</a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item" href="design-and-consultancy.html">Design & Consultancy</a></li>
                                            <li><a class="dropdown-item" href="solar-battery-ev.html">Solar, Battery & EV</a></li>
                                            <li><a class="dropdown-item" href="wind.html">Wind</a></li>
                                            <li><a class="dropdown-item" href="energy-audit-monitoring.html">Energy Audit & Monitoring</a></li>
                                            <li><a class="dropdown-item" href="solar-panel-servicing.html">Servicing</a></li>
                                        </ul>
                                    </li>
                                    <li><a class="nav-link" href="contact.html">Contact</a></li>
                                </ul>
                            </nav>
                        </div>
                        <!-- navigation area end -->
                    </div>
                </div>
                <div class="col-lg-3 col-md-3 col-6">
                    <div class="header-right">
                        <div class="action-button-menu">
                            <a href="contact.html" class="contact-btn">
                                <span class="btn-text">Let's Talk</span>
                                <span class="btn-icon"><i class="fa-solid fa-arrow-right"></i></span>
                            </a>
                            <!-- Mobile menu button removed -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>
    <!-- Header area end -->

    <!-- New Mobile Navigation Bar -->
    <nav class="mobile-navbar d-md-none">
        <div class="mobile-nav-container">
            <a href="index.html" class="mobile-nav-item active">
                <i class="fa-solid fa-home"></i>
                <span>Home</span>
            </a>
            <a href="aboutus.html" class="mobile-nav-item">
                <i class="fa-solid fa-info-circle"></i>
                <span>About</span>
            </a>
            <a href="project.html" class="mobile-nav-item">
                <i class="fa-solid fa-project-diagram"></i>
                <span>Projects</span>
            </a>
            <a href="service.html" class="mobile-nav-item">
                <i class="fa-solid fa-solar-panel"></i>
                <span>Services</span>
            </a>
            <a href="contact.html" class="mobile-nav-item">
                <i class="fa-solid fa-envelope"></i>
                <span>Contact</span>
            </a>
        </div>
    </nav>

    <!-- Legacy elements kept for compatibility -->
    <div id="side-bar" class="side-bar d-none"></div>
    <div id="anywhere-home" class="d-none"></div>

    <!-- Banner area start -->
    <section class="banner-section">
        <div class="rts-banner-six-area bg-image-solution banner-six-height">
            <video muted loop autoplay>
                <source src="media/18.webm" type="video/mp4">
            </video>
            <!-- Completely transparent overlay -->
            <div class="banner-overlay"></div>

            <div class="container">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="banner-six-inner-content-wrapper">
                            <div class="inner-content">
                                <span class="banner-tag">Sustainable Energy Solutions</span>
                                <h1 class="title">
                                    Powering a <span class="highlight-light">Sustainable</span> <br>
                                    Future with <span class="highlight-orange">Clean Energy</span>
                                </h1>
                                <p class="banner-description">Harness the power of renewable energy to reduce your carbon footprint and save on energy costs with our innovative solar, wind, and sustainable energy solutions.</p>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="banner-social-rotate">
                <!-- social wrapper start-->
                <div class="social-wrapper-one-horizental">
                    <a href="#"><i class="fa-brands fa-linkedin-in"></i></a>
                    <a href="#"><i class="fa-brands fa-youtube"></i></a>
                    <a href="#"><i class="fa-brands fa-twitter"></i></a>
                    <a href="#"><i class="fa-brands fa-facebook-f"></i></a>
                </div>
                <!-- social wrapper end -->
                <p class="follow">Follow us</p>
            </div>


        </div>
    </section>
    <!-- Banner area end -->

    <!-- About section start -->
    <section class="about-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center mb-5">
                    <div class="section-tag">About Us</div>
                    <h2 class="title skew-up">
                        Pioneering <span class="highlight-green">Renewable</span> Energy Solutions for a <span class="highlight-orange">Sustainable</span> Future
                    </h2>
                </div>
            </div>
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="about-content">
                        <div class="about-intro">
                            <p class="lead mb-4">
                                At REIN, we are dedicated to shaping a cleaner and more sustainable future through the power of renewable energy. With a passion for excellence and innovation, our team provides comprehensive solutions that empower individuals, businesses, and communities to embrace a greener way of life while enjoying significant cost savings.
                            </p>
                            <div class="mission-vision-box">
                                <div class="mission-box">
                                    <div class="icon green">
                                        <i class="fa-solid fa-bullseye"></i>
                                    </div>
                                    <h4>Our Mission</h4>
                                    <p>To accelerate the global transition to sustainable energy through innovative solutions and exceptional service.</p>
                                </div>
                                <div class="vision-box">
                                    <div class="icon orange">
                                        <i class="fa-solid fa-lightbulb"></i>
                                    </div>
                                    <h4>Our Vision</h4>
                                    <p>A world powered by clean, renewable energy that is accessible and affordable for everyone.</p>
                                </div>
                            </div>
                        </div>
                        <div class="key-points mt-4">
                            <h4 class="mb-3">Why Choose Our Solutions:</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="key-point">
                                        <div class="icon">
                                            <i class="fa-solid fa-check"></i>
                                        </div>
                                        <p>Comprehensive eco-friendly energy solutions</p>
                                    </div>
                                    <div class="key-point">
                                        <div class="icon">
                                            <i class="fa-solid fa-check"></i>
                                        </div>
                                        <p>Up to 70% reduction in energy bills</p>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="key-point">
                                        <div class="icon">
                                            <i class="fa-solid fa-check"></i>
                                        </div>
                                        <p>Expert consultation and 24/7 support</p>
                                    </div>
                                    <div class="key-point">
                                        <div class="icon">
                                            <i class="fa-solid fa-check"></i>
                                        </div>
                                        <p>Customized solutions for all sectors</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="team-profile-card mt-4">
                            <div class="profile-header">
                                <div class="profile-image-container">
                                    <img src="images/vishnu.jpg" alt="Vishnu Menon" class="profile-image">
                                </div>
                                <div class="profile-title">
                                    <h3>Vishnu Menon</h3>
                                    <span class="profile-position">Director</span>
                                </div>
                            </div>
                            <div class="profile-content">
                                <p class="profile-quote">"Our team combines technical expertise with a passion for sustainability to deliver exceptional results for our clients."</p>
                                <div class="profile-details">
                                    <div class="profile-detail-item">
                                        <i class="fa-solid fa-graduation-cap"></i>
                                        <span>Renewable Energy Expert</span>
                                    </div>
                                                    </div>
                                <div class="profile-social">
                                    <a href="https://www.linkedin.com/in/vishnu-menon-6130b7125" target="_blank" class="social-link linkedin">
                                        <i class="fa-brands fa-linkedin-in"></i>
                                    </a>
                                    <a href="mailto:<EMAIL>" class="social-link email">
                                        <i class="fa-solid fa-envelope"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <a class="button-circle-text mt-4" href="aboutus.html">
                            <i class="fa-solid fa-arrow-up-right"></i>
                            Learn More About Us
                        </a>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="about-image-container">
                        <div class="main-image-wrapper">
                            <img src="images/solar.jpg" alt="Renewable Energy Solutions" class="main-image">
                            <div class="image-overlay"></div>
                        </div>
                        <div class="stats-cards">
                            <div class="stat-card projects">
                                <div class="icon">
                                    <i class="fa-solid fa-solar-panel"></i>
                                </div>
                                <div class="stat-content">
                                    <h3>20+</h3>
                                    <p>Projects</p>
                                </div>
                            </div>
                            <div class="stat-card experience" style="border-left: 4px solid var(--secondary-color);">
                                <div class="icon">
                                    <i class="fa-solid fa-calendar-check"></i>
                                </div>
                                <div class="stat-content">
                                    <h3>2+</h3>
                                    <p>Years Exp.</p>
                                </div>
                            </div>
                            <div class="stat-card satisfaction">
                                <div class="icon">
                                    <i class="fa-solid fa-thumbs-up"></i>
                                </div>
                                <div class="stat-content">
                                    <h3>98%</h3>
                                    <p>Satisfaction</p>
                                </div>
                            </div>
                        </div>
                        <img class="floating-image" src="imagesprj/123.jpg" alt="Solar Installation">
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- About section end -->

    <!-- Services section start -->
    <section class="services-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="section-header">
                        <div class="title-area">
                            <div class="section-tag">Our Services</div>
                            <h2 class="title skew-up">Our Comprehensive <br>
                                <span class="highlight-green">Energy Solutions</span></h2>
                        </div>
                        <div class="button-area">
                            <a class="button-circle-text" href="service.html">
                                <i class="fa-solid fa-arrow-up-right"></i>
                                View All Services
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="service-card">
                        <div class="service-image">
                            <img src="images/solar.webp" alt="service">
                        </div>
                        <div class="service-content">
                            <div class="icon">
                                <img src="images/Wh.png" alt="icon">
                            </div>
                            <span class="service-category">Solar Setup</span>
                            <h4 class="service-title">Solar, Battery and EV</h4>
                            <a href="service-details.html" class="read-more">Read More <i class="fa-regular fa-arrow-right"></i></a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="service-card">
                        <div class="service-image">
                            <img src="images/wind.webp" alt="service">
                        </div>
                        <div class="service-content">
                            <div class="icon">
                                <img src="images/10.png" alt="icon">
                            </div>
                            <span class="service-category">Wind Energy</span>
                            <h4 class="service-title">Wind</h4>
                            <a href="service-details.html" class="read-more">Read More <i class="fa-regular fa-arrow-right"></i></a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="service-card">
                        <div class="service-image">
                            <img src="images/energy.png" alt="service">
                        </div>
                        <div class="service-content">
                            <div class="icon">
                                <img src="images/Wh.png" alt="icon">
                            </div>
                            <span class="service-category">Energy Management</span>
                            <h4 class="service-title">Energy Audit & Monitoring</h4>
                            <a href="service-details.html" class="read-more">Read More <i class="fa-regular fa-arrow-right"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Services section end -->

    <!-- Why Choose Us section start -->
    <section class="why-choose-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center">
                    <div class="section-tag">Why Choose Us</div>
                    <h2 class="title skew-up">Benefits of Choosing <span class="highlight-green">REIN</span></h2>
                    <p class="section-description">We deliver exceptional value through our commitment to quality, innovation, and customer satisfaction. Here's why our clients trust us for their renewable energy needs:</p>
                </div>
            </div>
            <div class="row g-4 mt-4">
                <div class="col-lg-3 col-md-6">
                    <div class="feature-card">
                        <div class="icon green">
                            <i class="fa-solid fa-certificate"></i>
                        </div>
                        <h4>Quality Assurance</h4>
                        <p>We use only premium-grade materials and equipment that meet international standards for performance and durability.</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="feature-card">
                        <div class="icon orange">
                            <i class="fa-solid fa-hand-holding-dollar"></i>
                        </div>
                        <h4>Cost Efficiency</h4>
                        <p>Our solutions are designed to maximize energy production while minimizing costs, providing excellent ROI.</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="feature-card">
                        <div class="icon green">
                            <i class="fa-solid fa-headset"></i>
                        </div>
                        <h4>Dedicated Support</h4>
                        <p>Our team provides 24/7 support and maintenance services to ensure your systems operate at peak efficiency.</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="feature-card">
                        <div class="icon orange">
                            <i class="fa-solid fa-leaf"></i>
                        </div>
                        <h4>Eco-Friendly</h4>
                        <p>By choosing our renewable energy solutions, you're contributing to a cleaner environment and a sustainable future.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Why Choose Us section end -->




    <!-- Contact section start -->
    <section class="contact-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-6">
                    <div class="contact-form-wrapper">
                        <div class="section-tag">Get In Touch</div>
                        <h2 class="title skew-up">Ready to Switch to <span class="highlight-green">Clean Energy?</span></h2>
                        <p>Fill out the form below and our team will get back to you within 24 hours to discuss your renewable energy needs.</p>
                        <form class="contact-form" id="contactForm"
                              action="https://formspree.io/f/movddbga"
                              method="POST"
                              data-netlify="true"
                              name="contact">
                            <!-- Netlify form name (hidden) -->
                            <input type="hidden" name="form-name" value="contact">
                            <!-- Formspree redirect URL -->
                            <input type="hidden" name="_next" value="https://revolutionenergyindia.in/thank-you.html">

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <input type="text" name="name" id="name" class="form-control" placeholder="Your Name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <input type="email" name="email" id="email" class="form-control" placeholder="Your Email" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <input type="tel" name="phone" id="phone" class="form-control" placeholder="Phone Number">
                                    </div>
                                </div>

                                <div class="col-12">
                                    <div class="form-group">
                                        <textarea name="message" id="message" class="form-control" rows="4" placeholder="Your Message" required></textarea>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">Send Message</button>
                                </div>
                                <div class="col-12 mt-3">
                                    <div id="form-message" class="alert" style="display: none;"></div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="contact-info-wrapper">
                        <div class="contact-info">
                            <div class="icon">
                                <i class="fa-solid fa-phone"></i>
                            </div>
                            <div class="content">
                                <h5>Call Us</h5>
                                <p><a href="javascript:void(0)" class="copy-number" data-number="9633126288">9633126288</a></p>
                            </div>
                        </div>
                        <div class="contact-info">
                            <div class="icon">
                                <i class="fa-solid fa-envelope"></i>
                            </div>
                            <div class="content">
                                <h5>Email Us</h5>
                                <p class="email-container">
                                    <a href="https://mail.google.com/mail/?view=cm&fs=1&to=<EMAIL>" target="_blank" title="<EMAIL>">
                                        <EMAIL>
                                    </a>
                                </p>
                                <p class="email-container">
                                    <a href="mailto:<EMAIL>"><EMAIL></a>
                                </p>
                            </div>
                        </div>
                        <div class="contact-info">
                            <div class="icon">
                                <i class="fa-solid fa-location-dot"></i>
                            </div>
                            <div class="content">
                                <h5>Visit Us</h5>
                                <p class="address-line">No 27 New, MGR Main Rd, Kandhanchavadi,</p>
                                <p class="address-line">Perungudi, Chennai, Tamil Nadu 600096, India</p>
                            </div>
                        </div>
                        <div class="contact-map">
                            <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3887.4657686057393!2d80.24863007486515!3d13.007999714280781!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3a5267a0673d1e8f%3A0x5b8f47d9b7e62f19!2sMGR%20Main%20Rd%2C%20Kandhanchavadi%2C%20Perungudi%2C%20Chennai%2C%20Tamil%20Nadu%20600096!5e0!3m2!1sen!2sin!4v1689321234567!5m2!1sen!2sin" width="100%" height="250" style="border:0;" allowfullscreen="" loading="lazy"></iframe>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Contact section end -->

    <!-- Footer start -->
    <footer class="footer">
        <div class="footer-top">
            <div class="container">
                <div class="row">
                    <div class="col-lg-4 col-md-6">
                        <div class="footer-widget about-widget">
                            <img src="images/logo-02.png" alt="logo" class="footer-logo">
                            <p>REIN is dedicated to providing sustainable energy solutions that help reduce carbon footprints while saving costs. Our mission is to accelerate the transition to clean, renewable energy.</p>
                            <div class="social-links">
                                <a href="#"><i class="fa-brands fa-facebook-f"></i></a>
                                <a href="#"><i class="fa-brands fa-twitter"></i></a>
                                <a href="#"><i class="fa-brands fa-linkedin-in"></i></a>
                                <a href="#"><i class="fa-brands fa-youtube"></i></a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-6">
                        <div class="footer-widget">
                            <h4 class="footer-title">Quick Links</h4>
                            <ul class="footer-links">
                                <li><a href="index.html">Home</a></li>
                                <li><a href="aboutus.html">About Us</a></li>
                                <li><a href="service.html">Services</a></li>
                                <li><a href="project.html">Projects</a></li>
                                <li><a href="contact.html">Contact</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="footer-widget">
                            <h4 class="footer-title">Our Services</h4>
                            <ul class="footer-links">
                                <li><a href="#">Solar Installation</a></li>
                                <li><a href="#">Wind Energy Solutions</a></li>
                                <li><a href="#">Energy Audit & Monitoring</a></li>
                                <li><a href="#">Maintenance & Support</a></li>
                                <li><a href="#">Consultancy Services</a></li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="footer-widget">
                            <h4 class="footer-title">Newsletter</h4>
                            <p>Subscribe to our newsletter to receive updates on the latest renewable energy trends and our services.</p>
                            <form class="newsletter-form">
                                <input type="email" placeholder="Your Email Address">
                                <button type="submit"><i class="fa-solid fa-paper-plane"></i></button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <div class="container">
                <div class="row">
                    <div class="col-md-6">
                        <p>&copy; 2025 REIN. All Rights Reserved.</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <p>Designed with <i class="fa-solid fa-heart"></i> for a greener future</p>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    <!-- Footer end -->

    <!-- JS Files -->
    <script src="js/jquery-3.6.0.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/swiper.min.js"></script>
    <script src="js/metismenu.js"></script>
    <script src="js/wow.min.js"></script>
    <script src="js/main.js"></script>

    <!-- No external email service scripts needed for Formspree -->



    <!-- Custom JS for copying phone numbers, form handling, and mobile menu -->
    <script>
        // Initialize mobile navbar
        console.log('Mobile navbar initialization');

        document.addEventListener('DOMContentLoaded', function() {
            // Add click event listeners to all elements with class 'copy-number'
            const phoneNumbers = document.querySelectorAll('.copy-number');

            phoneNumbers.forEach(function(element) {
                element.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Get the phone number from the data attribute
                    const phoneNumber = this.getAttribute('data-number');

                    // Create a temporary input element
                    const tempInput = document.createElement('input');
                    tempInput.value = phoneNumber;
                    document.body.appendChild(tempInput);

                    // Select and copy the text
                    tempInput.select();
                    document.execCommand('copy');

                    // Remove the temporary element
                    document.body.removeChild(tempInput);

                    // Show feedback to the user
                    const originalText = this.textContent;
                    this.textContent = 'Copied!';

                    // Reset the text after a short delay
                    setTimeout(() => {
                        this.textContent = originalText;
                    }, 1500);
                });
            });

            // Contact form submission handling with Formspree
            const contactForm = document.getElementById('contactForm');
            if (contactForm) {
                contactForm.addEventListener('submit', function(e) {
                    // We don't prevent default form submission since Formspree handles it
                    // But we can add some visual feedback

                    const formMessage = document.getElementById('form-message');
                    const submitButton = contactForm.querySelector('button[type="submit"]');

                    // Disable submit button and show loading state
                    submitButton.disabled = true;
                    submitButton.innerHTML = '<i class="fa-solid fa-spinner fa-spin"></i> Sending...';

                    // Show a temporary sending message
                    formMessage.style.display = 'block';
                    formMessage.className = 'alert alert-info';
                    formMessage.innerHTML = '<i class="fa-solid fa-paper-plane"></i> Sending your message...';

                    // The form will be submitted normally to Formspree
                    // We'll add a small delay to show the sending message
                    setTimeout(() => {
                        // The page will redirect to Formspree's thank you page
                        // or stay on the page if there's an error
                    }, 1000);
                });
            }

            // Mobile Navbar Functionality - Simplified
            console.log('Mobile navbar initialized - Services dropdown removed');

            // Set active class for current page in mobile navbar
            const currentPath = window.location.pathname;
            const mobileNavItems = document.querySelectorAll('.mobile-nav-item');

            mobileNavItems.forEach(item => {
                const href = item.getAttribute('href');
                if (href && currentPath.includes(href)) {
                    // Remove active class from all items
                    mobileNavItems.forEach(i => i.classList.remove('active'));
                    // Add active class to current item
                    item.classList.add('active');
                }
            });

            // Desktop dropdown functionality
            const desktopDropdowns = document.querySelectorAll('.header-nav .has-dropdown');
            desktopDropdowns.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.classList.add('active');
                    $(this).find('.submenu').stop().slideDown(300);
                });

                item.addEventListener('mouseleave', function() {
                    this.classList.remove('active');
                    $(this).find('.submenu').stop().slideUp(300);
                });

                // Add click functionality for touch devices
                const dropdownLink = item.querySelector('a');
                dropdownLink.addEventListener('click', function(e) {
                    if (window.innerWidth > 991) {
                        // Only for desktop/tablet
                        if (!item.classList.contains('active')) {
                            e.preventDefault();
                            item.classList.add('active');
                            $(item).find('.submenu').stop().slideDown(300);
                        }
                    }
                });
            });

            // MetisMenu already initialized above
        });
    </script>
</body>
</html>
